'use client';

import { useEffect } from 'react';
import <PERSON>rip<PERSON> from 'next/script';
import { trackEvent, isAnalyticsEnabled } from '../utils/analytics';

interface GoogleAnalyticsProps {
  measurementId: string;
}

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
    gaTrackingEnabled?: boolean;
  }
}



const GoogleAnalytics = ({ measurementId }: GoogleAnalyticsProps) => {
  useEffect(() => {
    // Initialize Google Consent Mode v2
    if (typeof window !== 'undefined') {
      // Default consent state (denied until user gives consent)
      window.gtag = window.gtag || function() {
        (window.dataLayer = window.dataLayer || []).push(arguments);
      };

      window.gtag('consent', 'default', {
        'analytics_storage': 'denied',
        'ad_storage': 'denied',
        'ad_user_data': 'denied',
        'ad_personalization': 'denied',
        'functionality_storage': 'granted',
        'security_storage': 'granted',
        'wait_for_update': 500,
      });

      window.gtag('config', measurementId, {
        page_title: document.title,
        page_location: window.location.href,
      });

      // Enhanced scroll tracking with multiple depth levels
      let scrollDepths = {
        25: false,
        50: false,
        75: false,
        100: false
      };

      const handleScroll = () => {
        if (!isAnalyticsEnabled()) return;

        const scrollPercent = Math.round(
          (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
        );

        // Track scroll depths
        Object.keys(scrollDepths).forEach(depth => {
          const depthNum = parseInt(depth);
          if (scrollPercent >= depthNum && !scrollDepths[depthNum as keyof typeof scrollDepths]) {
            scrollDepths[depthNum as keyof typeof scrollDepths] = true;
            trackEvent('scroll_depth', {
              category: 'engagement',
              label: `${depth}%`,
              value: depthNum
            });
          }
        });
      };

      // Navigation click tracking
      const trackNavigationClick = (event: Event) => {
        if (!isAnalyticsEnabled()) return;

        const target = event.target as HTMLElement;
        const link = target.closest('a, button');

        if (link) {
          const text = link.textContent?.trim() || '';
          const href = link.getAttribute('href') || '';

          // Track navigation menu clicks
          if (link.closest('nav') || link.closest('[role="navigation"]')) {
            trackEvent('navigation_click', {
              category: 'navigation',
              label: text,
              page_location: href
            });
          }

          // Track CTA button clicks
          if (link.textContent?.includes('Contattaci') ||
              link.classList.contains('cta-button') ||
              link.textContent?.includes('Scopri') ||
              link.textContent?.includes('Inizia')) {
            trackEvent('cta_click', {
              category: 'conversion',
              label: text,
              page_location: href
            });
          }

          // Track external link clicks
          if (href.startsWith('http') && !href.includes(window.location.hostname)) {
            trackEvent('external_link_click', {
              category: 'outbound',
              label: href,
              page_location: window.location.href
            });
          }
        }
      };

      // Image click tracking
      const trackImageClick = (event: Event) => {
        if (!isAnalyticsEnabled()) return;

        const target = event.target as HTMLElement;
        const img = target.closest('img') || target.querySelector('img');

        if (img) {
          const src = img.getAttribute('src') || '';
          const alt = img.getAttribute('alt') || '';

          // Determine image category
          let category = 'image_click';
          if (src.includes('portfolio') || alt.toLowerCase().includes('portfolio')) {
            category = 'portfolio_image';
          } else if (src.includes('collaboration') || alt.toLowerCase().includes('collaboration')) {
            category = 'collaboration_logo';
          }

          trackEvent('image_click', {
            category,
            label: alt || src,
            image_url: src
          });
        }
      };

      // Add event listeners
      window.addEventListener('scroll', handleScroll, { passive: true });
      document.addEventListener('click', trackNavigationClick);
      document.addEventListener('click', trackImageClick);

      // Cleanup function
      return () => {
        window.removeEventListener('scroll', handleScroll);
        document.removeEventListener('click', trackNavigationClick);
        document.removeEventListener('click', trackImageClick);
      };
    }
  }, [measurementId]);

  return (
    <>
      {/* Google Analytics gtag.js */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            
            // Initialize consent mode
            gtag('consent', 'default', {
              'analytics_storage': 'denied',
              'ad_storage': 'denied',
              'ad_user_data': 'denied',
              'ad_personalization': 'denied',
              'functionality_storage': 'granted',
              'security_storage': 'granted',
              'wait_for_update': 500,
            });
            
            gtag('config', '${measurementId}', {
              page_title: document.title,
              page_location: window.location.href,
            });
          `,
        }}
      />
    </>
  );
};


export default GoogleAnalytics;
